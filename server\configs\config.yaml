# Please refer to the LICENSE file in the root directory of the project.
# https://github.com/kubesphere/console/blob/master/LICENSE

# kubesphere configuration
server:
  http:
    hostname: localhost
    port: 8000
    static:
      development:
        /public: server/public
        /assets: packages/core/src/assets
        /build: build
        /dist: dist
      production:
        /public: server/public
        /assets: dist/assets
        /dist: dist

  # redis config for multi replicas
  # redis:
  #   port: 6379
  #   host: 127.0.0.1
  redisTimeout: 5000

  # kubesphere console session params, not login session
  sessionKey: 'kubesphere:sess'
  sessionTimeout: 3600000 # unit: millisecond

  # backend service gateway server
  apiServer:
    clientID: kubesphere
    clientSecret: kubesphere
    url: http://************:31349
    wsUrl: ws://************:31349

  # docker image search default url
  dockerHubUrl: https://hub.docker.com

  # default theme
  theme:
    tabTitle: 'KubeSphere'
    title: 'ENT_TITLE'
    logo: /assets/logo.svg
    favicon: /assets/favicon.ico
    background: ''
    description: 'KS_DESCRIPTION'

client:
  title: KubeSphere
  description: ''
  encryptKey: kubesphere

  version:
    kubesphere: 4.1.0

  homePage: /dashboard

  # kubesphere urls
  issueUrl: https://github.com/kubesphere/kubesphere/issues/new/choose
  reposUrl: https://github.com/kubesphere/kubesphere
  slackUrl: https://kubesphere.slack.com
  documents:
    en:
      url: https://docs.kubesphere.com.cn/v4.0
      api: https://kubesphere.io/docs/reference/api-docs/
    zh:
      url: https://docs.kubesphere.com.cn/v4.0
      api: https://kubesphere.com.cn/docs/reference/api-docs/

  # current support lanaguages
  supportLangs:
    - label: '简体中文'
      value: 'zh'
    - label: 'English'
      value: 'en'
  defaultLang: 'en'

  # top-bar navigations
  topbarNavs:
    name: topbar
    children:
      # - name: platform
      #   title: Platform
      #   icon: cogwheel
      - name: extensions_marketplace
        title: KUBESPHERE_MARKETPLACE
        icon: PlugCircle
      - name: apps
        title: APP_STORE
        icon: appcenter
      - name: workbench
        title: Workbench
        icon: dashboard
  # platform management navigations
  globalNavs:
    name: platform
    children:
      #      []
      - name: apps-manage
        title: CLUSTER_MANAGEMENT
        desc: CLUSTER_DESC
        icon: cluster
        roleTemplate: global_aaa_manager
      # - name: access
      #   title: ACCESS_CONTROL
      #   icon: key
      #   authKey: users|roles|workspaces
      # - name: settings
      #   title: PLATFORM_SETTINGS
      #   icon: cogwheel
      #   authKey: platform-settings
      #   authAction: manage
  # edge node group navigations
  nodeGroupNavs:
    - cate: node-groups
      children:
        - { name: nodes, title: NODE_ADMINISTRATION_PL, icon: nodes }
        - { name: projects, title: PROJECT_PL, icon: project }
        - name: settings
          title: NODE_GROUP_SETTINGS_PL
          icon: cogwheel
          children:
            - { name: base-info, title: BASIC_INFORMATION }
            - { name: edgeippool, title: 容器池 IP 组 }

  clusterNavs:
    name: cluster
    children:
      - name: overview
        title: OVERVIEW
        icon: dashboard
        showInDisable: true
      - name: nodes
        title: NODE_PL
        icon: nodes
        children:
          - { name: nodes, title: CLUSTER_NODE_PL }
      # - { name: components, title: SYSTEM_COMPONENT_PL, icon: components }
      - { name: projects, title: PROJECT_PL, icon: project }
      - name: app-workloads
        title: APPLICATION_WORKLOAD_PL
        icon: appcenter
        authKey: project-resources
        children:
          - name: workloads
            title: WORKLOAD_PL
            tabs:
              - { name: deployments, title: DEPLOYMENT_PL }
              - { name: statefulsets, title: STATEFULSET_PL }
              - { name: daemonsets, title: DAEMONSET_PL }
          - name: jobs
            title: JOB_PL
            tabs:
              - { name: jobs, title: JOB_PL }
              - { name: cronjobs, title: CRONJOB_PL }
          - { name: pods, title: POD_PL }
          - { name: services, title: SERVICE_PL }
          - { name: ingresses, title: ROUTE_PL }
          # - {
          #   name: clusteringresssettings,
          #   title: ROUTE_SETTING_PL,
          #   # ksModule: clusteringresssettings,
          #   # clusterModule: clusteringresssettings
          # }

      - name: config
        title: CONFIGURATION
        icon: hammer
        children:
          - { name: secrets, title: SECRET_PL }
          - { name: configmaps, title: CONFIGMAP_PL }
          - { name: serviceaccounts, title: SERVICE_ACCOUNT_PL, requiredClusterVersion: v3.1.0 }
      - name: network
        title: NETWORK
        icon: earth
        children:
          - {
              name: networkpolicies,
              title: NETWORK_POLICY_PL,
              clusterModule: network,
              ksModule: network,
            }
          - { name: ippools, title: POD_IP_POOL_PL, clusterModule: network, ksModule: network }
      # Contains dangerous operations, only available for platform-admin.
      - { name: customresources, title: CRD_PL, icon: select, admin: true }
      - name: storage
        title: STORAGE
        icon: database
        children:
          - { name: volumes, title: PERSISTENT_VOLUME_CLAIM_PL }
          - { name: storageclasses, title: STORAGE_CLASS_PL }
          # - {
          #   name: volume-snapshots,
          #   title: VOLUME_SNAPSHOT_PL,
          #   ksModule: storage-utils,
          # }
          # - {
          #   name: volume-snapshot-classes,
          #   title: VOLUME_SNAPSHOT_CLASS_PL,
          #   ksModule: storage-utils,
          #   authKey: volumesnapshots,
          # }
      - name: monitoring-alerting
        title: MONITORING_AND_ALERTING
        icon: monitor
        children: []
        # children:
        #   - {
        #     name: monitor-cluster,
        #     title: CLUSTER_STATUS,
        #     icon: linechart,
        #     authKey: monitoring,
        #     clusterModule: whizard-monitoring,
        #   }
        #   - {
        #     name: monitor-resource,
        #     title: APPLICATION_RESOURCE_PL,
        #     icon: linechart,
        #     authKey: monitoring,
        #     clusterModule: whizard-monitoring,
        #   }
        #   - {
        #     name: custom-monitoring,
        #     title: CUSTOM_MONITORING,
        #     clusterModule: whizard-monitoring,
        #     requiredClusterVersion: v3.1.0,
        #   }
      - name: cluster-settings
        title: CLUSTER_SETTINGS
        icon: cogwheel
        showInDisable: true
        children:
          - { name: base-info, title: BASIC_INFORMATION, showInDisable: true }
          - {
              name: visibility,
              title: CLUSTER_VISIBILITY,
              authKey: cluster-settings,
              multiCluster: true,
            }
          - { name: members, title: CLUSTER_MEMBER_PL }
          - { name: roles, title: CLUSTER_ROLE_PL }
          - {
              name: log-collections,
              title: LOG_RECEIVER_PL,
              icon: file,
              authKey: cluster-settings,
              ksModuleAnnotation: 'whizard-notification|logging.kubesphere.io/enable-notification-history',
              clusterModulesSome: whizard-logging|whizard-events|whizard-auditing|whizard-notification,
              ksModulesSome: whizard-logging|whizard-events|whizard-auditing,
            }

  # access control page navigations

  accessNavs:
    name: 'access'
    children:
      # - { name: workspaces, title: WORKSPACE_PL, icon: enterprise }
      - { name: accounts, title: USER_PL, icon: human, authKey: users }
      - { name: roles, title: PLATFORM_ROLE_PL, icon: role }

  # platform settings navigations
  platformSettingsNavs:
    name: 'platformsettings'
    children:
      - name: base-info
        title: PLATFORM_INFORMATION
        icon: home
        authKey: platform-settings

  # workspace page navigations
  workspaceNavs:
    name: 'workspace'
    children:
      - { name: overview, title: OVERVIEW, icon: dashboard, authKey: 'projects', order: 0 }
      - name: projects
        title: PROJECT_PL
        icon: project
        order: 0.1
        children:
          - { name: projects, title: PROJECT_PL, skipAuth: true }
          # - {
          #     name: federatedprojects,
          #     title: MULTI_CLUSTER_PROJECT_PL,
          #     ksModule: "kubefed",
          #   }
      #      - {
      #        name: devops,
      #        title: DEVOPS_PROJECT_PL,
      #        icon: strategy-group,
      #        ksModule: devops,
      #        skipAuth: true,
      #      }
      - name: apps # backend not ready
        title: APPS_MANAGEMENT
        icon: appcenter
        children:
          - name: app-repos
            title: APP_REPOSITORY_PL
            authKey: app-repos
      - name: management
        title: WORKSPACE_SETTINGS
        icon: cogwheel
        children:
          - { name: base-info, title: Basic Info, skipAuth: true }
          - { name: quota, title: WORKSPACE_QUOTA_PL, authKey: 'workspace-settings' }
          - { name: roles, title: WORKSPACE_ROLE_PL }
          - { name: members, title: WORKSPACE_MEMBER_PL }
          # - { name: groups, title: DEPARTMENT_PL }

  # project page navigations
  projectNavs:
    name: 'project'
    children:
      - {
          name: overview,
          title: OVERVIEW,
          icon: dashboard,
          authKey: project-settings,
          authAction: namespace-view-project-settings,
        }
      #      - name: app-deploy-manage
      #        title: APP_DEPLOY
      #        icon: appcenter
      #        authKey: app-releases
      #        authAction: namespace-view-app-releases
      #        children:
      #          - { name: deploy, title: APPLICATION, authKey: app-releases }
      - name: app-workloads
        title: APPLICATION_WORKLOAD_PL
        icon: appcenter
        children:
          - { name: deploy, title: APPLICATION, authKey: app-releases }
          #          - { name: applications/template, title: APPLICATION }
          - { name: services, title: SERVICE_PL }
          - name: workloads
            title: WORKLOAD_PL
            tabs:
              - { name: deployments, title: DEPLOYMENT_PL }
              - { name: statefulsets, title: STATEFULSET_PL }
              - { name: daemonsets, title: DAEMONSET_PL }
          - name: jobs
            title: JOB_PL
            tabs:
              - { name: jobs, title: JOB_PL }
              - { name: cronjobs, title: CRONJOB_PL }
          - { name: ingresses, title: ROUTE_PL }
          - { name: pods, title: POD_PL }

      - name: storage
        title: STORAGE
        icon: database
        children:
          - { name: volumes, title: PERSISTENT_VOLUME_CLAIM_PL, authKey: persistentvolumeclaims }
          # - {
          #   name: volume-snapshots,
          #   title: VOLUME_SNAPSHOT_PL,
          #   ksModule: storage-utils,
          #   authKey: volumesnapshots,
          # }
      - name: config
        title: CONFIGURATION
        icon: hammer
        children:
          - { name: secrets, title: SECRET_PL }
          - { name: configmaps, title: CONFIGMAP_PL }
          - { name: serviceaccounts, title: SERVICE_ACCOUNT_PL, requiredClusterVersion: v3.1.0 }
          # - {
          #   name: grayrelease,
          #   title: GRAYSCALE_RELEASE,
          #   icon: bird,
          #   clusterModule: servicemesh,
          #   }
          # - {
          #     name: composing,
          #     title: APP_PL,
          #     icon: application,
          #     clusterModule: servicemesh,
          # }
          # - {
          #   name: s2ibuilders,
          #   title: IMAGE_BUILDER_PL,
          #   icon: vnas,
          #   clusterModule: devops,
          # }
      - name: monitoring
        title: MONITORING_AND_ALERTING
        icon: monitor
        children: []
        # children:
        # - {
        #   name: alerts,
        #   title: ALERTING_MESSAGE_PL,
        #   icon: loudspeaker,
        #   clusterModule: whizard-alerting,
        # }
        # - {
        #   name: alert-rules,
        #   title: ALERTING_POLICY_PL,
        #   icon: hammer,
        #   clusterModule: whizard-alerting,
        #   authKey: 'rulegroups'
        # }
        # - {
        #   name: custom-monitoring,
        #   title: CUSTOM_MONITORING,
        #   clusterModule: whizard-monitoring,
        #   requiredClusterVersion: v3.1.0,
        # }
      - name: settings
        title: PROJECT_SETTINGS
        icon: cogwheel
        children:
          - {
              name: base-info,
              title: Basic Info,
              authKey: project-settings,
              authAction: namespace-view-project-settings,
            }
          - {
              name: networkpolicies,
              title: NETWORK_ISOLATION,
              clusterModule: network,
              ksModule: network,
              authKey: project-settings,
            }
          - { name: roles, title: PROJECT_ROLE_PL }
          - { name: members, title: PROJECT_MEMBER_PL }
          - {
              name: log-collections,
              title: LOG_COLLECTION,
              ksModuleAnnotationClusters: 'whizard-logging|logging.kubesphere.io/enable-loggingsidecar',
              ksModule: whizard-logging,
              authKey: 'project-settings',
            }

  federatedProjectNavs:
    cate: 'project'
    children:
      - { name: overview, title: OVERVIEW, icon: dashboard, skipAuth: true }
      - name: app-workloads
        title: APPLICATION_WORKLOAD_PL
        icon: appcenter
        children:
          - { name: applications, title: COMPOSED_APP_PL, icon: application }
          - { name: services, title: SERVICE_PL }
          - name: workloads
            title: WORKLOAD_PL
            tabs:
              - { name: deployments, title: DEPLOYMENT_PL }
              - { name: statefulsets, title: STATEFULSET_PL }
          - { name: ingresses, title: ROUTE_PL }
      - { name: volumes, title: PERSISTENT_VOLUME_CLAIM, icon: storage }
      - name: config
        title: CONFIGURATION
        icon: hammer
        children:
          - { name: secrets, title: SECRET_PL }
          - { name: configmaps, title: CONFIGMAP_PL }
      - name: settings
        title: PROJECT_SETTINGS
        icon: cogwheel
        children:
          - { name: base-info, title: BASIC_INFORMATION, skipAuth: true }
          - { name: quota, title: PROJECT_QUOTA_PL, authKey: 'project-settings' }
          - {
              name: log-collections,
              title: LOG_COLLECTION,
              ksModuleAnnotationClusters: 'whizard-logging|logging.kubesphere.io/enable-loggingsidecar',
              ksModule: whizard-logging,
              authKey: 'project-settings',
            }

  # devops page navigations
  devopsNavs:
    name: 'devops'
    children:
      - { name: pipelines, title: PIPELINE_PL, icon: application }
      - {
          name: cd,
          title: CONTINUOUS_DEPLOYMENT_PL,
          icon: rocket,
          authKey: 'applications',
          # requiredClusterVersion: v3.3.0,
        }
      - {
          name: code-repo,
          title: CODE_REPO_PL,
          icon: code,
          authKey: 'gitrepositories',
          # requiredClusterVersion: v3.3.0,
        }
      - name: management
        title: DEVOPS_PROJECT_SETTINGS
        icon: cogwheel
        open: true
        children:
          - { name: base-info, title: BASIC_INFORMATION, skipAuth: true }
          - { name: credentials, title: CREDENTIAL_PL }
          - { name: roles, title: DEVOPS_PROJECT_ROLE_PL }
          - { name: members, title: DEVOPS_PROJECT_MEMBER_PL }

  # toolbox nav
  toolboxNavs:
    name: 'toolbox'
    children:
      - name: analysis
        title: ANALYSIS_TOOLS
        children:
          - {
              name: log-query,
              title: CONTAINER_LOG_SEARCH,
              description: CONTAINER_LOG_SEARCH_DESC,
              icon: file,
              enabled: true,
            }
          - {
              name: event-search,
              title: RESOURCE_EVENT_SEARCH,
              description: RESOURCE_EVENT_SEARCH_DESC,
              icon: thunder,
            }
          - {
              name: auditing-search,
              title: AUDIT_LOG_SEARCH,
              description: AUDIT_LOG_DESC,
              icon: login-servers,
              enabled: true,
            }
          - {
              name: bill,
              title: RESOURCE_CONSUMPTION_STATISTICS,
              description: METERING_AND_BILLING_DESC,
              icon: wallet,
            }
          - {
              name: kubeconfig,
              title: kubeconfig,
              description: VIEW_KUBE_CONFIG,
              icon: documentation,
              enabled: true,
            }
      - name: control
        title: CONTROL_TOOL
        children:
          - {
              name: kubectl,
              title: kubectl,
              description: KUBECTL_DESC,
              icon: terminal,
              enabled: true,
            }

  alertingCenterNavs:
    name: 'alerting'
    children:
      - cate: 'alertingCenter'
        items:
          - name: message
            title: ALERTING_MESSAGE_PL
            icon: loudspeaker
            skipAuth: true
          - name: policy
            title: ALERTING_POLICY_PL
            icon: bell
            skipAuth: true

  # whizard telemetry navigations
  whizardTelemetryNavs:
    name: 'whizardTelemetry'
    children:
      - name: resource-monitoring
        title: WHIZARD_MONITORING.RESOURCE_MONITORING
        icon: dashboard
        children:
          - name: overview
            title: WHIZARD_MONITORING.MULTI_CLUSTER_MONITOR
            skipAuth: true
          - name: ranking
            title: WHIZARD_MONITORING.RESOURCE_RANKING_PL
            skipAuth: true
      - name: global-alarm
        title: GLOBAL_ALERTS
        icon: alarm-lamp
        ksModule: whizard-alerting
        children:
          - name: alerting/builtin-alerts
            title: WHIZARD_MONITORING.BUILT_IN_ALERTING_PL
            skipAuth: true
          - name: alerting/custom-alerts
            title: WHIZARD_MONITORING.CUSTOM_ALERTING_PL
            skipAuth: true
          - name: alerting/builtin-rules
            title: BUILT_IN_POLICIES
            skipAuth: true
          - name: alerting/custom-rules
            title: CUSTOM_POLICIES
            skipAuth: true
      - name: resource-filter
        title: WHIZARD_MONITORING.RESOURCE_FILTER
        icon: magnifier
        children:
          - name: pods
            title: POD_PL
            skipAuth: true

  observabilityNavs:
    name: 'observability'
    children:
      - cate: observability
        items:
          - name: overview
            title: OVERVIEW
            icon: dashboard
            skipAuth: true
            showInDisable: true
          - name: nodes
            title: NODE_PL
            icon: nodes
            children:
              - { name: nodes, title: CLUSTER_NODE_PL }
              - { name: edgenodes, title: EDGE_NODE_PL, clusterModule: edgeruntime }
          - { name: projects, title: PROJECT_PL, icon: project }
          - name: app-workloads
            title: APPLICATION_WORKLOAD_PL
            icon: appcenter
            children:
              - name: workloads
                title: WORKLOAD_PL
                tabs:
                  - { name: deployments, title: DEPLOYMENT_PL }
                  - { name: statefulsets, title: STATEFULSET_PL }
                  - { name: daemonsets, title: DAEMONSET_PL }
              - name: jobs
                title: JOB_PL
                tabs:
                  - { name: jobs, title: JOB_PL }
                  - { name: cronjobs, title: CRONJOB_PL }
              - { name: pods, title: POD_PL }
              - { name: services, title: SERVICE_PL }
              - { name: ingresses, title: ROUTE_PL }
          # Contains dangerous operations, only available for platform-admin.
          - name: storage
            title: STORAGE
            icon: database
            children:
              - { name: volumes, title: PERSISTENT_VOLUME_CLAIM_PL }
              - { name: storageclasses, title: STORAGE_CLASS_PL }
              - {
                  name: volume-snapshots,
                  title: VOLUME_SNAPSHOT_PL,
                  ksModule: 'storage-utils',
                  clusterModule: 'storage-utils',
                  authKey: 'volumesnapshots',
                }
              - {
                  name: volume-snapshot-classes,
                  title: VOLUME_SNAPSHOT_CLASS_PL,
                  ksModule: 'storage-utils',
                  clusterModule: 'storage-utils',
                  authKey: 'volumesnapshotclasses',
                }
          - name: monitoring-alerting
            title: MONITORING_AND_ALERTING
            icon: monitor
            children:
              - {
                  name: alerts,
                  title: ALERTING_MESSAGE_PL,
                  icon: loudspeaker,
                  clusterModule: whizard-alerting,
                }
              - {
                  name: alert-rules,
                  title: ALERTING_POLICY_PL,
                  icon: hammer,
                  clusterModule: whizard-alerting,
                  authKey: 'rulegroups',
                }
              - {
                  name: monitor-cluster,
                  title: CLUSTER_STATUS,
                  icon: linechart,
                  authKey: monitoring,
                }
              - {
                  name: monitor-resource,
                  title: APPLICATION_RESOURCE_PL,
                  icon: linechart,
                  authKey: monitoring,
                }
              # - {
              #     name: custom-monitoring,
              #     title: CUSTOM_MONITORING,
              #     requiredClusterVersion: v3.1.0,
              #   }
          - name: gateways
            title: GATEWAY_SETTINGS
            icon: loadbalancer
            clusterModule: 'gateway'
            authKey: 'gateway'

  # system workspace rules control
  systemWorkspace: system-workspace
  showOutSiteLink: true
  systemWorkspaceRules:
    devops: []
    members: [manage]
    projects: [view, edit, manage]
    roles: [view]
    workspaces: [view, edit]
  systemWorkspaceProjectRules:
    alerting: [manage]
    members: [manage]
    roles: [manage]
    custom-monitoring: [manage]

  # preset infos
  presetUsers: [admin, sonarqube]
  presetGlobalRoles:
    [platform-admin, platform-regular, workspaces-manager, users-manager, platform-self-provisioner]
  presetClusterRoles: [cluster-admin, cluster-viewer]
  presetWorkspaceRoles: [admin, regular, viewer, self-provisioner]
  presetDevOpsRoles: [admin, operator, viewer]
  presetRoles: [admin, operator, viewer]

  # system annotations that need to be hidden for edit
  preservedAnnotations: ['.*kubesphere.io/', 'openpitrix_runtime']

  # namespaces that need to be disable collection file log
  disabledLoggingSidecarNamespace: ['kubesphere-logging-system']

  # loadbalancer annotations, default support qingcloud lb
  loadBalancerDefaultAnnotations:
    service.beta.kubernetes.io/qingcloud-load-balancer-eip-ids: ''
    service.beta.kubernetes.io/qingcloud-load-balancer-type: '0'

  # control error notify on page
  enableErrorNotify: true

  # enable image search when add image for a container
  enableImageSearch: true

  # development
  # disable authorization check when developing
  disableAuthorization: false

  # show kubeconfig
  enableKubeConfig: true

  # third party tools
  thirdPartyTools: []
  # - title:
  #   description:
  #   link:

  # TODO：need update to v4.0 docs
  # docs url for resources
  resourceDocs:
    # Project User Guide
    applications: /project-user-guide/application/app-template/
    composingapps: /project-user-guide/application/compose-app/
    deployments: /07-cluster-management/05-application-workloads/01-workloads/
    statefulsets: /07-cluster-management/05-application-workloads/01-workloads/
    daemonsets: /07-cluster-management/05-application-workloads/01-workloads/
    jobs: /project-user-guide/application-workloads/jobs/
    cronjobs: /project-user-guide/application-workloads/cronjobs/
    services: /project-user-guide/application-workloads/services/
    ingresses: /project-user-guide/application-workloads/routes/
    s2i_template: /project-user-guide/image-builder/s2i-templates/
    volumes: /project-user-guide/storage/volumes/
    secrets: /project-user-guide/configuration/secrets/
    configmaps: /project-user-guide/configuration/configmaps/
    serviceaccounts: /project-user-guide/configuration/serviceaccounts/
    imageregistry: /project-user-guide/configuration/image-registry/
    grayrelease: /project-user-guide/grayscale-release/overview/

    # Project Administration
    internet: /project-administration/project-gateway/
    project_base_info: /workspace-administration/project-quotas/
    project_roles: /project-administration/role-and-member-management/
    project_members: /project-administration/role-and-member-management/

    # Cluster Administrator Guide
    nodes: /cluster-administration/nodes/
    storageclasses: /cluster-administration/storageclass/

    # DevOps User Guide
    pipelines: /devops-user-guide/how-to-use/pipeline-settings/
    cridentials: /devops-user-guide/how-to-use/credential-management/

    # Developer Guide
    helm_specification: /application-store/app-developer-guide/helm-specification/
    helm_developer_guide: /application-store/app-developer-guide/helm-developer-guide/

    # Custom Console
    custom_console: /cluster-administration/platform-settings/customize-basic-information/

    # Kube Config
    kube_config: /10-toolbox/02-view-a-kubeconfig-file/
  # devops:
  #  replace sonarqube link to customize url origin, default is useless
  # sonarqubeURL: http://***************:8080

  #notification management
  notification:
    mail:
      max_number_of_eamil: 50
    dingtalk:
      max_number_of_cid: 20
      max_number_of_keyword: 20
    feishu:
      max_number_of_cid: 20
      max_number_of_keyword: 20
    wecom:
      max_number_of_toUser: 50
      max_number_of_toParty: 20
      max_number_of_toTag: 20
    slack:
      max_number_of_channel: 20

  # supported Gpu type
  supportGpuType:
    - 'nvidia.com/gpu'
  supportLinks:
    kse:
      en: https://kubesphere.co/kse
      zh: https://kubesphere.com.cn/kse
    ksc:
      en: https://kubesphere.cloud/en/
      zh: https://kubesphere.cloud/
    github: 'https://github.com/kubesphere/kubesphere'
    githubPr: 'https://github.com/kubesphere/kubesphere'
    star: 'https://github.com/kubesphere/kubesphere'
    githubFork: 'https://github.com/kubesphere/kubesphere'
    slack: 'https://kubesphere.slack.com/'
    doc: 'https://kubesphere.io/zh/docs/v4.1/'
    forum: 'https://kubesphere.com.cn/forum/'
    issues: 'https://github.com/kubesphere/kubesphere/issues'
    contactUs: 'https://m.qingcloud.com/page/23555798970015596/4c97b2026cb84249be20d94e71b647cf'
    backup: 'https://kubesphere.cloud/self-service/disaster-recovery/'
    inspection: 'https://kubesphere.cloud/inspection/'
    light: 'https://kubesphere.cloud/lite-cluster/'

  # license page is hide unauthorized components
  # isHideUnauthorizedComponents: false
  isHideKsCoreLicenseResourceUsage: false

  # Can the cluster management node list open the terminal?
  enableNodeListTerminal: false
